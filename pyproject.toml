[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "ai-terminal"
version = "1.0.0"
description = "Sophisticated autonomous AI-powered CLI terminal application"
readme = "README.md"
license = {text = "MIT"}
authors = [
    {name = "AI Terminal Team", email = "<EMAIL>"}
]
classifiers = [
    "Development Status :: 5 - Production/Stable",
    "Environment :: Console",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Topic :: Software Development :: Tools",
    "Topic :: System :: Shells",
    "Topic :: Artificial Intelligence",
]
keywords = ["ai", "terminal", "cli", "assistant", "automation", "agent"]
requires-python = ">=3.11"
dependencies = [
    # Core dependencies
    "textual>=0.45.0,<0.50.0",
    "rich>=13.7.0",
    "click>=8.1.7",
    "pydantic>=2.5.0",
    "aiohttp>=3.9.0",
    "aiofiles>=23.2.1",
    
    # Database and storage
    "sqlalchemy>=2.0.23",
    "aiosqlite>=0.19.0",
    "alembic>=1.13.0",
    "cryptography>=41.0.7",
    
    # LLM providers
    "openai>=1.6.0",
    "anthropic>=0.8.0",
    "google-generativeai>=0.3.0",
    "mistralai>=0.0.12",
    
    # Utilities
    "python-dotenv>=1.0.0",
    "toml>=0.10.2",
    "psutil>=5.9.6",
    "gitpython>=3.1.40",
    "pygments>=2.17.0",
    "prompt-toolkit>=3.0.41",
    "watchdog>=3.0.0",
    "httpx>=0.25.2",
    
    # Security
    "keyring>=24.3.0",
    "bcrypt>=4.1.2",
    
    # File operations
    "pathspec>=0.11.2",
    "chardet>=5.2.0",
    
    # Development tools integration
    "black>=23.11.0",
    "isort>=5.12.0",
    "mypy>=1.7.0",
    "pylint>=3.0.3",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.4.3",
    "pytest-asyncio>=0.21.1",
    "pytest-cov>=4.1.0",
    "pytest-mock>=3.12.0",
    "pre-commit>=3.6.0",
    "sphinx>=7.2.6",
    "sphinx-rtd-theme>=1.3.0",
]
test = [
    "pytest>=7.4.3",
    "pytest-asyncio>=0.21.1",
    "pytest-cov>=4.1.0",
    "pytest-mock>=3.12.0",
]
docs = [
    "sphinx>=7.2.6",
    "sphinx-rtd-theme>=1.3.0",
    "myst-parser>=2.0.0",
]

[project.urls]
Homepage = "https://github.com/ai-terminal/ai-terminal"
Documentation = "https://ai-terminal.readthedocs.io"
Repository = "https://github.com/ai-terminal/ai-terminal"
"Bug Tracker" = "https://github.com/ai-terminal/ai-terminal/issues"

[project.scripts]
ai-terminal = "ai_terminal.main:main"
ait = "ai_terminal.main:main"

[tool.setuptools.packages.find]
where = ["."]
include = ["ai_terminal*"]
exclude = ["tests*"]

[tool.setuptools.package-data]
ai_terminal = ["py.typed", "data/*", "templates/*"]

# Black configuration
[tool.black]
line-length = 88
target-version = ['py39']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

# isort configuration
[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
known_first_party = ["ai_terminal"]

# mypy configuration
[tool.mypy]
python_version = "3.9"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[[tool.mypy.overrides]]
module = [
    "textual.*",
    "rich.*",
    "anthropic.*",
    "mistralai.*",
    "google.generativeai.*",
]
ignore_missing_imports = true

# pytest configuration
[tool.pytest.ini_options]
minversion = "7.0"
addopts = "-ra -q --strict-markers --strict-config"
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
    "unit: marks tests as unit tests",
]

# Coverage configuration
[tool.coverage.run]
source = ["ai_terminal"]
omit = [
    "*/tests/*",
    "*/test_*",
    "*/__pycache__/*",
    "*/migrations/*",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]

# Pylint configuration
[tool.pylint.messages_control]
disable = [
    "C0330",  # Wrong hanging indentation
    "C0326",  # Bad whitespace
    "R0903",  # Too few public methods
    "R0913",  # Too many arguments
    "W0613",  # Unused argument
]

[tool.pylint.format]
max-line-length = 88
